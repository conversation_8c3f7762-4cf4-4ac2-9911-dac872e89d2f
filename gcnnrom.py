# ==============================================================================
# 第1部分：环境准备与依赖
# ==============================================================================
#
# 说明：
# 在开始之前需要安装必要的库：用于高效数据处理的 pandas、数值计算的 numpy，
# 稀疏矩阵计算的 scipy，深度学习与稀疏传播的 torch（PyTorch）。

# --- 安装指引 ---
# 注意：请在终端或代码单元中运行以下命令。
# pip install -U numpy scipy pandas torch

import pandas as pd
import numpy as np
import torch
from scipy import sparse
from typing import Dict, List, Tuple

print("依赖库已成功导入。")

# ==============================================================================
# 第2部分：数据加载
# ==============================================================================
#
# 说明：
# 程序将从 `user.csv`、`book.csv` 与 `inter.csv` 读取数据。
# 推荐模型的核心是交互数据（`inter.csv`），它记录了用户借阅图书的事实，属于“隐式反馈”。
# 借阅行为可视为对该图书的正向偏好。

# --- 数据加载 ---
print("\n正在从CSV文件加载数据...")
try:
    interactions = pd.read_csv(r'D:\Desktop\bookac_optimized\data\inter.csv')
    users = pd.read_csv(r'D:\Desktop\bookac_optimized\data\user.csv')
    books = pd.read_csv(r'D:\Desktop\bookac_optimized\data\book.csv')
    print("数据加载成功。")
    print(f"找到 {len(interactions)} 条交互、{len(users)} 位用户、{len(books)} 本图书。")
except FileNotFoundError as e:
    print(f"错误：{e}。请确保CSV文件位于正确的目录。")
    exit()


# ==============================================================================
# 第3部分：构建二部图与索引映射
# ==============================================================================
#
# 说明：
# 使用 `interactions[user_id, book_id]` 构建二部图：用户-物品边；并建立ID映射。
print("\n正在构建索引映射与二部图...")

inter_df = interactions[['user_id', 'book_id']].dropna().drop_duplicates()
unique_users = pd.Index(inter_df['user_id'].astype(str).unique())
unique_items = pd.Index(inter_df['book_id'].astype(str).unique())

uid2idx: Dict[str, int] = {uid: i for i, uid in enumerate(unique_users)}
bid2idx: Dict[str, int] = {bid: i for i, bid in enumerate(unique_items)}
idx2uid: Dict[int, str] = {i: uid for uid, i in uid2idx.items()}
idx2bid: Dict[int, str] = {i: bid for bid, i in bid2idx.items()}

n_users = len(unique_users)
n_items = len(unique_items)

# 行列索引向量（用户索引、物品索引）
u_idx = inter_df['user_id'].astype(str).map(uid2idx).values
i_idx = inter_df['book_id'].astype(str).map(bid2idx).values
data_vals = np.ones(len(inter_df), dtype=np.float32)

# 用户-物品交互稀疏矩阵 R（n_users x n_items）
R = sparse.coo_matrix((data_vals, (u_idx, i_idx)), shape=(n_users, n_items)).tocsr()
print(f"R 矩阵：shape=({n_users}, {n_items})，nnz={R.nnz}")

# 冷启动回退所需的物品流行度序列
popular_items: List[str] = inter_df['book_id'].astype(str).value_counts().index.tolist()


# ==============================================================================
# 第4部分：LightGCN 模型（PyTorch 精简实现）- 修正版
# ==============================================================================
#
# 说明：
# 此版本修正了 RuntimeError，通过在每个批次内重新计算前向传播来保证
# PyTorch 自动求导机制的正确性。同时保留了对初始嵌入进行正则化的标准 BPR 损失。

class LightGCN(torch.nn.Module):
    def __init__(self, num_users: int, num_items: int, embed_dim: int = 64, n_layers: int = 3):
        super().__init__()
        self.num_users = num_users
        self.num_items = num_items
        self.embed_dim = embed_dim
        self.n_layers = n_layers

        # 可训练的初始嵌入
        self.user_emb = torch.nn.Embedding(num_users, embed_dim)
        self.item_emb = torch.nn.Embedding(num_items, embed_dim)

        # 使用 Xavier 初始化改善收敛性
        torch.nn.init.xavier_uniform_(self.user_emb.weight)
        torch.nn.init.xavier_uniform_(self.item_emb.weight)

    def forward(self, norm_adj: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # forward pass 执行图传播过程
        initial_embs = torch.cat([self.user_emb.weight, self.item_emb.weight], dim=0)
        all_embs = [initial_embs]

        # 逐层传播
        current_embs = initial_embs
        for _ in range(self.n_layers):
            current_embs = torch.sparse.mm(norm_adj, current_embs)
            all_embs.append(current_embs)

        # 层聚合：对所有层的嵌入（包括第0层）取平均
        final_embs = torch.mean(torch.stack(all_embs, dim=0), dim=0)

        # 分离用户和物品的最终嵌入
        users, items = torch.split(final_embs, [self.num_users, self.num_items], dim=0)
        return users, items


def build_norm_adj(R: sparse.csr_matrix) -> torch.Tensor:
    # 构建二部图邻接矩阵 A = [[0, R], [R^T, 0]]，并做 D^{-1/2} A D^{-1/2}
    n_users, n_items = R.shape
    A = sparse.vstack([
        sparse.hstack([sparse.csr_matrix((n_users, n_users)), R]),
        sparse.hstack([R.T, sparse.csr_matrix((n_items, n_items))])
    ]).tocsr()

    deg = np.array(A.sum(axis=1)).flatten()
    deg[deg == 0] = 1.0
    d_inv_sqrt = np.power(deg, -0.5)
    D_inv_sqrt = sparse.diags(d_inv_sqrt)
    S = D_inv_sqrt @ A @ D_inv_sqrt

    S_coo = S.tocoo()
    indices = torch.tensor(np.vstack((S_coo.row, S_coo.col)), dtype=torch.long)
    values = torch.tensor(S_coo.data, dtype=torch.float32)
    shape = torch.Size(S_coo.shape)

    return torch.sparse_coo_tensor(indices, values, size=shape).coalesce()


print("\n正在构建规范化邻接矩阵并初始化 LightGCN...")
S_sp = build_norm_adj(R)

device = torch.device("cpu")
model = LightGCN(num_users=n_users, num_items=n_items, embed_dim=64, n_layers=3).to(device)
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)

# 为 BPR 采样准备：每个用户的正样本列表
pos_items_by_user: Dict[int, List[int]] = {}
for u, i in zip(u_idx, i_idx):
    pos_items_by_user.setdefault(int(u), []).append(int(i))


def sample_batch(batch_size: int = 4096) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    users = np.random.randint(0, n_users, size=batch_size)
    pos = np.array([np.random.choice(pos_items_by_user[u]) for u in users])
    neg = np.random.randint(0, n_items, size=batch_size)

    for k in range(3):
        mask = np.array([neg[m] in pos_items_by_user.get(users[m], []) for m in range(batch_size)])
        if not mask.any():
            break
        neg[mask] = np.random.randint(0, n_items, size=mask.sum())

    return (
        torch.tensor(users, dtype=torch.long, device=device),
        torch.tensor(pos, dtype=torch.long, device=device),
        torch.tensor(neg, dtype=torch.long, device=device),
    )


def bpr_loss(final_u_emb: torch.Tensor, final_i_emb: torch.Tensor, final_j_emb: torch.Tensor,
             initial_u_emb: torch.Tensor, initial_i_emb: torch.Tensor, initial_j_emb: torch.Tensor,
             reg: float = 1e-4) -> torch.Tensor:
    # BPR 目标函数，使用传播后的最终嵌入
    pos_scores = torch.sum(final_u_emb * final_i_emb, dim=1)
    neg_scores = torch.sum(final_u_emb * final_j_emb, dim=1)
    bpr_objective = -torch.nn.functional.logsigmoid(pos_scores - neg_scores).mean()

    # L2 正则化项，作用于可训练的初始嵌入
    reg_term = reg * (
            initial_u_emb.norm(2).pow(2) +
            initial_i_emb.norm(2).pow(2) +
            initial_j_emb.norm(2).pow(2)
    ) / len(final_u_emb)

    return bpr_objective + reg_term


print("\n开始训练 LightGCN（BPR）...")
epochs = 100
batch_size = 4096
reg_lambda = 1e-4
S_sp = S_sp.to(device)

for epoch in range(1, epochs + 1):
    model.train()
    total_loss = 0.0
    steps = max(1, (R.nnz // batch_size))

    # --- 修正：将前向传播移回批次循环内 ---
    # 这确保了每个 backward() 调用都有一个新的计算图，从而避免 RuntimeError。
    for _ in range(steps):
        users_b, pos_b, neg_b = sample_batch(batch_size)

        # 1. 在每个批次中执行完整的前向传播
        final_user_emb, final_item_emb = model(S_sp)

        # 2. 从传播后的嵌入中获取批次数据
        u_emb_final = final_user_emb[users_b]
        i_emb_final = final_item_emb[pos_b]
        j_emb_final = final_item_emb[neg_b]

        # 3. 从模型的可训练层中获取初始嵌入，用于计算正则化损失
        u_emb_initial = model.user_emb(users_b)
        i_emb_initial = model.item_emb(pos_b)
        j_emb_initial = model.item_emb(neg_b)

        # 4. 计算总损失
        loss = bpr_loss(
            u_emb_final, i_emb_final, j_emb_final,
            u_emb_initial, i_emb_initial, j_emb_initial,
            reg=reg_lambda
        )

        # 5. 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += float(loss.item())

    avg_loss = total_loss / steps
    print(f"Epoch {epoch:02d} | loss={avg_loss:.4f}")

print("模型训练完成。")

# ==============================================================================
# 第5部分：生成与保存推荐结果
# ==============================================================================
#
# 说明：
# 为每位用户生成 Top-1 推荐；若用户在训练集中无记录（冷启动），回退到全局最热图书。

print("\n正在为所有用户生成推荐...")
all_user_ids = users['借阅人'].astype(str).unique().tolist()

# 训练后一次性前向，得到最终嵌入
model.eval()
with torch.no_grad():
    user_all, item_all = model(S_sp)

# 为过滤已交互集合做准备
seen_items_by_user = (
    inter_df.groupby('user_id')['book_id']
    .apply(lambda s: set(s.astype(str).tolist()))
    .to_dict()
)

rec_pairs: List[Tuple[str, str]] = []
item_all_t = item_all.t()  # [D, I]

for uid in all_user_ids:
    if uid in uid2idx:
        uidx = uid2idx[uid]
        u_vec = user_all[uidx:uidx+1]           # [1, D]
        scores = torch.matmul(u_vec, item_all_t).squeeze(0)  # [I]
        # 将已看过物品的分数置为 -inf
        for seen_bid in seen_items_by_user.get(uid, set()):
            if seen_bid in bid2idx:
                scores[bid2idx[seen_bid]] = -1e9
        top_idx = int(torch.topk(scores, k=1).indices[0].item())
        rec_pairs.append((uid, idx2bid[top_idx]))
    else:
        # 冷启动：按全局最热未看过的第一个
        seen = seen_items_by_user.get(uid, set())
        fallback_bid = None
        for it in popular_items:
            if it not in seen:
                fallback_bid = it
                break
        if fallback_bid is None and len(popular_items) > 0:
            fallback_bid = popular_items[0]
        if fallback_bid is not None:
            rec_pairs.append((uid, fallback_bid))

# 构建 DataFrame
recommendations_df = pd.DataFrame(rec_pairs, columns=['user_id', 'book_id'])

# 保存为提交文件
output_filename = 'submission.csv'
recommendations_df.to_csv(output_filename, index=False)

print(f"\n已成功生成并保存 {len(recommendations_df)} 条推荐结果。")
print(f"输出文件: '{output_filename}'")
print("最终推荐示例：")
print(recommendations_df.head())
