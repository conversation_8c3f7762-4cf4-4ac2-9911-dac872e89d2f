# ==============================================================================
# 第1部分：环境准备与依赖
# ==============================================================================
#
# 说明：
# 在开始之前需要安装必要的库：用于高效数据处理的 pandas、数值计算的 numpy，
# 稀疏矩阵计算的 scipy，深度学习与稀疏传播的 torch（PyTorch）。

# --- 安装指引 ---
# 注意：请在终端或代码单元中运行以下命令。
# pip install -U numpy scipy pandas torch

import pandas as pd
import numpy as np
import torch
from scipy import sparse
from typing import Dict, List, Tuple
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

print("依赖库已成功导入。")

# ==============================================================================
# 第2部分：数据加载与特征预处理
# ==============================================================================
#
# 说明：
# 程序将从 `user.csv`、`book.csv` 与 `inter.csv` 读取数据。
# 推荐模型的核心是交互数据（`inter.csv`），它记录了用户借阅图书的事实，属于“隐式反馈”。
# 借阅行为可视为对该图书的正向偏好。

# --- 数据加载 ---
print("\n正在从CSV文件加载数据...")
try:
    interactions = pd.read_csv('data/inter.csv')
    users = pd.read_csv('data/user.csv')
    books = pd.read_csv('data/book.csv')
    print("数据加载成功。")
    print(f"找到 {len(interactions)} 条交互、{len(users)} 位用户、{len(books)} 本图书。")
except FileNotFoundError as e:
    print(f"错误：{e}。请确保CSV文件位于正确的目录。")
    exit()

# --- 特征预处理 ---
print("\n正在预处理用户和物品特征...")

# 用户特征编码
user_features = users.copy()
dept_encoder = LabelEncoder()
grade_encoder = LabelEncoder()
type_encoder = LabelEncoder()

user_features['dept_encoded'] = dept_encoder.fit_transform(user_features['DEPT'].fillna('未知'))
user_features['grade_encoded'] = grade_encoder.fit_transform(user_features['年级'].astype(str).fillna('未知'))
user_features['type_encoded'] = type_encoder.fit_transform(user_features['类型'].fillna('未知'))

# 物品特征编码
book_features = books.copy()
cat1_encoder = LabelEncoder()
cat2_encoder = LabelEncoder()

book_features['cat1_encoded'] = cat1_encoder.fit_transform(book_features['一级分类'].fillna('未知'))
book_features['cat2_encoded'] = cat2_encoder.fit_transform(book_features['二级分类'].fillna('未知'))

print(f"用户特征维度：院系{len(dept_encoder.classes_)}，年级{len(grade_encoder.classes_)}，类型{len(type_encoder.classes_)}")
print(f"物品特征维度：一级分类{len(cat1_encoder.classes_)}，二级分类{len(cat2_encoder.classes_)}")


# ==============================================================================
# 第3部分：智能评分构建与索引映射
# ==============================================================================
#
# 说明：
# 使用 `interactions[user_id, book_id]` 构建二部图：用户-物品边；并建立ID映射。
# 教授建议1：构建更有意义的隐式评分，而不是简单的二值化

def build_implicit_rating(inter_df):
    """构建基于续借次数和借阅时长的智能评分"""
    ratings = []
    for _, row in inter_df.iterrows():
        base_score = 1.0

        # 续借加分（续借说明很喜欢）
        renew_bonus = min(row.get('续借次数', 0) * 0.5, 2.0)

        # 借阅时长加分
        if pd.notna(row.get('还书时间')) and pd.notna(row.get('借阅时间')):
            try:
                borrow_days = (pd.to_datetime(row['还书时间']) -
                              pd.to_datetime(row['借阅时间'])).days
                time_bonus = min(borrow_days / 30.0, 1.0)  # 最多1分
            except:
                time_bonus = 0.5  # 解析失败时的默认值
        else:
            time_bonus = 0.5  # 默认值

        final_score = base_score + renew_bonus + time_bonus
        ratings.append(min(final_score, 5.0))  # 限制最高5分

    return np.array(ratings, dtype=np.float32)

print("\n正在构建索引映射与智能评分...")

# 准备交互数据，包含所有可用列
available_cols = ['user_id', 'book_id']
if '续借次数' in interactions.columns:
    available_cols.append('续借次数')
if '借阅时间' in interactions.columns:
    available_cols.append('借阅时间')
if '还书时间' in interactions.columns:
    available_cols.append('还书时间')

inter_df = interactions[available_cols].dropna(subset=['user_id', 'book_id']).drop_duplicates(subset=['user_id', 'book_id'])
unique_users = pd.Index(inter_df['user_id'].astype(str).unique())
unique_items = pd.Index(inter_df['book_id'].astype(str).unique())

uid2idx: Dict[str, int] = {uid: i for i, uid in enumerate(unique_users)}
bid2idx: Dict[str, int] = {bid: i for i, bid in enumerate(unique_items)}
idx2uid: Dict[int, str] = {i: uid for uid, i in uid2idx.items()}
idx2bid: Dict[int, str] = {i: bid for bid, i in bid2idx.items()}

n_users = len(unique_users)
n_items = len(unique_items)

# 行列索引向量（用户索引、物品索引）
u_idx = inter_df['user_id'].astype(str).map(uid2idx).values
i_idx = inter_df['book_id'].astype(str).map(bid2idx).values

# 教授建议1：构建智能评分而非简单的1
print("正在构建基于续借和时长的智能评分...")
data_vals = build_implicit_rating(inter_df)
print(f"评分统计：最小值={data_vals.min():.2f}，最大值={data_vals.max():.2f}，平均值={data_vals.mean():.2f}")

# 用户-物品交互稀疏矩阵 R（n_users x n_items）
R = sparse.coo_matrix((data_vals, (u_idx, i_idx)), shape=(n_users, n_items)).tocsr()
print(f"R 矩阵：shape=({n_users}, {n_items})，nnz={R.nnz}")

# 冷启动回退所需的物品流行度序列
popular_items: List[str] = inter_df['book_id'].astype(str).value_counts().index.tolist()

# 构建用户和物品特征映射
print("正在构建特征映射...")
user_feat_map = {}
for _, row in user_features.iterrows():
    uid = str(row['借阅人'])
    if uid in uid2idx:
        user_feat_map[uid2idx[uid]] = {
            'dept': int(row['dept_encoded']),
            'grade': int(row['grade_encoded']),
            'type': int(row['type_encoded'])
        }

item_feat_map = {}
for _, row in book_features.iterrows():
    bid = str(row['book_id'])
    if bid in bid2idx:
        item_feat_map[bid2idx[bid]] = {
            'cat1': int(row['cat1_encoded']),
            'cat2': int(row['cat2_encoded'])
        }

print(f"用户特征覆盖率：{len(user_feat_map)}/{n_users} ({100*len(user_feat_map)/n_users:.1f}%)")
print(f"物品特征覆盖率：{len(item_feat_map)}/{n_items} ({100*len(item_feat_map)/n_items:.1f}%)")


# ==============================================================================
# 第4部分：增强LightGCN模型 - 融合Side Information
# ==============================================================================
#
# 说明：
# 教授建议3：加入用户和物品的Side Information来增强模型表现
# 此版本修正了 RuntimeError，通过在每个批次内重新计算前向传播来保证
# PyTorch 自动求导机制的正确性。同时保留了对初始嵌入进行正则化的标准 BPR 损失。

class EnhancedLightGCN(torch.nn.Module):
    def __init__(self, num_users: int, num_items: int, embed_dim: int = 128, n_layers: int = 4,
                 num_depts: int = 50, num_grades: int = 10, num_types: int = 5,
                 num_cat1: int = 100, num_cat2: int = 500):
        super().__init__()
        self.num_users = num_users
        self.num_items = num_items
        self.embed_dim = embed_dim
        self.n_layers = n_layers

        # 主要的用户和物品嵌入
        self.user_emb = torch.nn.Embedding(num_users, embed_dim)
        self.item_emb = torch.nn.Embedding(num_items, embed_dim)

        # 教授建议3：用户特征嵌入（院系、年级、类型）
        self.dept_emb = torch.nn.Embedding(num_depts, 16)
        self.grade_emb = torch.nn.Embedding(num_grades, 8)
        self.type_emb = torch.nn.Embedding(num_types, 8)

        # 教授建议3：图书特征嵌入（分类）
        self.cat1_emb = torch.nn.Embedding(num_cat1, 16)
        self.cat2_emb = torch.nn.Embedding(num_cat2, 16)

        # 特征融合层
        self.user_proj = torch.nn.Linear(embed_dim + 32, embed_dim)  # 16+8+8=32
        self.item_proj = torch.nn.Linear(embed_dim + 32, embed_dim)  # 16+16=32

        # 使用 Xavier 初始化改善收敛性
        for module in [self.user_emb, self.item_emb, self.dept_emb, self.grade_emb,
                      self.type_emb, self.cat1_emb, self.cat2_emb]:
            torch.nn.init.xavier_uniform_(module.weight)

        # 线性层初始化
        torch.nn.init.xavier_uniform_(self.user_proj.weight)
        torch.nn.init.xavier_uniform_(self.item_proj.weight)

    def forward(self, norm_adj: torch.Tensor, user_feats: torch.Tensor = None,
                item_feats: torch.Tensor = None) -> Tuple[torch.Tensor, torch.Tensor]:
        # 获取基础嵌入
        base_user_emb = self.user_emb.weight
        base_item_emb = self.item_emb.weight

        # 如果有特征，进行特征增强
        if user_feats is not None:
            dept_emb = self.dept_emb(user_feats[:, 0])
            grade_emb = self.grade_emb(user_feats[:, 1])
            type_emb = self.type_emb(user_feats[:, 2])
            user_feat_concat = torch.cat([base_user_emb, dept_emb, grade_emb, type_emb], dim=1)
            enhanced_user_emb = self.user_proj(user_feat_concat)
        else:
            enhanced_user_emb = base_user_emb

        if item_feats is not None:
            cat1_emb = self.cat1_emb(item_feats[:, 0])
            cat2_emb = self.cat2_emb(item_feats[:, 1])
            item_feat_concat = torch.cat([base_item_emb, cat1_emb, cat2_emb], dim=1)
            enhanced_item_emb = self.item_proj(item_feat_concat)
        else:
            enhanced_item_emb = base_item_emb

        # forward pass 执行图传播过程
        initial_embs = torch.cat([enhanced_user_emb, enhanced_item_emb], dim=0)
        all_embs = [initial_embs]

        # 逐层传播
        current_embs = initial_embs
        for _ in range(self.n_layers):
            current_embs = torch.sparse.mm(norm_adj, current_embs)
            all_embs.append(current_embs)

        # 层聚合：对所有层的嵌入（包括第0层）取平均
        final_embs = torch.mean(torch.stack(all_embs, dim=0), dim=0)

        # 分离用户和物品的最终嵌入
        users, items = torch.split(final_embs, [self.num_users, self.num_items], dim=0)
        return users, items

print("\n正在构建索引映射与二部图...")

inter_df = interactions[['user_id', 'book_id']].dropna().drop_duplicates()
unique_users = pd.Index(inter_df['user_id'].astype(str).unique())
unique_items = pd.Index(inter_df['book_id'].astype(str).unique())

uid2idx: Dict[str, int] = {uid: i for i, uid in enumerate(unique_users)}
bid2idx: Dict[str, int] = {bid: i for i, bid in enumerate(unique_items)}
idx2uid: Dict[int, str] = {i: uid for uid, i in uid2idx.items()}
idx2bid: Dict[int, str] = {i: bid for bid, i in bid2idx.items()}

n_users = len(unique_users)
n_items = len(unique_items)

# 行列索引向量（用户索引、物品索引）
u_idx = inter_df['user_id'].astype(str).map(uid2idx).values
i_idx = inter_df['book_id'].astype(str).map(bid2idx).values

# 教授建议1：构建智能评分而非简单的1
print("正在构建基于续借和时长的智能评分...")
data_vals = build_implicit_rating(inter_df)
print(f"评分统计：最小值={data_vals.min():.2f}，最大值={data_vals.max():.2f}，平均值={data_vals.mean():.2f}")

# 用户-物品交互稀疏矩阵 R（n_users x n_items）
R = sparse.coo_matrix((data_vals, (u_idx, i_idx)), shape=(n_users, n_items)).tocsr()
print(f"R 矩阵：shape=({n_users}, {n_items})，nnz={R.nnz}")

# 冷启动回退所需的物品流行度序列
popular_items: List[str] = inter_df['book_id'].astype(str).value_counts().index.tolist()


def build_norm_adj(R: sparse.csr_matrix) -> torch.Tensor:
    # 构建二部图邻接矩阵 A = [[0, R], [R^T, 0]]，并做 D^{-1/2} A D^{-1/2}
    n_users, n_items = R.shape
    A = sparse.vstack([
        sparse.hstack([sparse.csr_matrix((n_users, n_users)), R]),
        sparse.hstack([R.T, sparse.csr_matrix((n_items, n_items))])
    ]).tocsr()

    deg = np.array(A.sum(axis=1)).flatten()
    deg[deg == 0] = 1.0
    d_inv_sqrt = np.power(deg, -0.5)
    D_inv_sqrt = sparse.diags(d_inv_sqrt)
    S = D_inv_sqrt @ A @ D_inv_sqrt

    S_coo = S.tocoo()
    indices = torch.tensor(np.vstack((S_coo.row, S_coo.col)), dtype=torch.long)
    values = torch.tensor(S_coo.data, dtype=torch.float32)
    shape = torch.Size(S_coo.shape)

    return torch.sparse_coo_tensor(indices, values, size=shape).coalesce()


# 准备特征张量
print("正在准备特征张量...")
user_feat_tensor = torch.zeros((n_users, 3), dtype=torch.long)
item_feat_tensor = torch.zeros((n_items, 2), dtype=torch.long)

for uidx, feats in user_feat_map.items():
    user_feat_tensor[uidx] = torch.tensor([feats['dept'], feats['grade'], feats['type']])

for iidx, feats in item_feat_map.items():
    item_feat_tensor[iidx] = torch.tensor([feats['cat1'], feats['cat2']])

print("\n正在构建规范化邻接矩阵并初始化增强LightGCN...")
S_sp = build_norm_adj(R)

device = torch.device("cpu")
# 教授建议：更好的超参数组合
model = EnhancedLightGCN(
    num_users=n_users,
    num_items=n_items,
    embed_dim=128,      # 增加到128
    n_layers=4,         # 增加到4层
    num_depts=len(dept_encoder.classes_),
    num_grades=len(grade_encoder.classes_),
    num_types=len(type_encoder.classes_),
    num_cat1=len(cat1_encoder.classes_),
    num_cat2=len(cat2_encoder.classes_)
).to(device)

# 教授建议：更好的优化器设置
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.8)

user_feat_tensor = user_feat_tensor.to(device)
item_feat_tensor = item_feat_tensor.to(device)

# 为 BPR 采样准备：每个用户的正样本列表
pos_items_by_user: Dict[int, List[int]] = {}
for u, i in zip(u_idx, i_idx):
    pos_items_by_user.setdefault(int(u), []).append(int(i))


def sample_batch_improved(batch_size: int = 8192) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """教授建议2：混合随机+热门负采样策略"""
    users = np.random.randint(0, n_users, size=batch_size)
    pos = np.array([np.random.choice(pos_items_by_user[u]) for u in users])

    # 改进：混合随机+热门负采样
    neg = []
    popular_item_indices = [bid2idx[bid] for bid in popular_items[:500] if bid in bid2idx]

    for i, u in enumerate(users):
        if np.random.random() < 0.7:  # 70%随机采样
            neg_item = np.random.randint(0, n_items)
        else:  # 30%热门但用户未交互的物品
            # 从热门物品中采样未交互的
            user_pos_items = set(pos_items_by_user.get(u, []))
            popular_uninteracted = [idx for idx in popular_item_indices
                                  if idx not in user_pos_items]
            if popular_uninteracted:
                neg_item = np.random.choice(popular_uninteracted)
            else:
                neg_item = np.random.randint(0, n_items)
        neg.append(neg_item)

    neg = np.array(neg)

    # 确保负样本不在正样本中（最多重试3次）
    for retry in range(3):
        mask = np.array([neg[m] in pos_items_by_user.get(users[m], []) for m in range(batch_size)])
        if not mask.any():
            break
        neg[mask] = np.random.randint(0, n_items, size=mask.sum())

    return (
        torch.tensor(users, dtype=torch.long, device=device),
        torch.tensor(pos, dtype=torch.long, device=device),
        torch.tensor(neg, dtype=torch.long, device=device),
    )


def bpr_loss(final_u_emb: torch.Tensor, final_i_emb: torch.Tensor, final_j_emb: torch.Tensor,
             initial_u_emb: torch.Tensor, initial_i_emb: torch.Tensor, initial_j_emb: torch.Tensor,
             reg: float = 1e-5) -> torch.Tensor:
    # BPR 目标函数，使用传播后的最终嵌入
    pos_scores = torch.sum(final_u_emb * final_i_emb, dim=1)
    neg_scores = torch.sum(final_u_emb * final_j_emb, dim=1)
    bpr_objective = -torch.nn.functional.logsigmoid(pos_scores - neg_scores).mean()

    # L2 正则化项，作用于可训练的初始嵌入
    reg_term = reg * (
            initial_u_emb.norm(2).pow(2) +
            initial_i_emb.norm(2).pow(2) +
            initial_j_emb.norm(2).pow(2)
    ) / len(final_u_emb)

    return bpr_objective + reg_term


# ==============================================================================
# 第5部分：模型融合训练策略
# ==============================================================================
print("\n开始训练多个模型变体进行融合...")

# 教授建议：训练多个模型变体
models = {}
model_configs = [
    {'name': 'enhanced_v1', 'embed_dim': 128, 'n_layers': 4},
    {'name': 'enhanced_v2', 'embed_dim': 96, 'n_layers': 3},
    {'name': 'enhanced_v3', 'embed_dim': 160, 'n_layers': 4},
]

# 教授建议：调整训练参数
epochs = 200  # 增加训练轮数
batch_size = 8192  # 增加batch size
reg_lambda = 1e-5  # 降低正则化
S_sp = S_sp.to(device)

for config in model_configs:
    print(f"\n训练模型 {config['name']}...")

    model = EnhancedLightGCN(
        num_users=n_users,
        num_items=n_items,
        embed_dim=config['embed_dim'],
        n_layers=config['n_layers'],
        num_depts=len(dept_encoder.classes_),
        num_grades=len(grade_encoder.classes_),
        num_types=len(type_encoder.classes_),
        num_cat1=len(cat1_encoder.classes_),
        num_cat2=len(cat2_encoder.classes_)
    ).to(device)

    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.8)

    # 为 BPR 采样准备：每个用户的正样本列表
    pos_items_by_user: Dict[int, List[int]] = {}
    for u, i in zip(u_idx, i_idx):
        pos_items_by_user.setdefault(int(u), []).append(int(i))

    for epoch in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        steps = max(1, (R.nnz // batch_size))

        # --- 修正：将前向传播移回批次循环内 ---
        # 这确保了每个 backward() 调用都有一个新的计算图，从而避免 RuntimeError。
        for step in range(steps):
            users_b, pos_b, neg_b = sample_batch_improved(batch_size)

            # 1. 在每个批次中执行完整的前向传播（带特征）
            final_user_emb, final_item_emb = model(S_sp, user_feat_tensor, item_feat_tensor)

            # 2. 从传播后的嵌入中获取批次数据
            u_emb_final = final_user_emb[users_b]
            i_emb_final = final_item_emb[pos_b]
            j_emb_final = final_item_emb[neg_b]

            # 3. 从模型的可训练层中获取初始嵌入，用于计算正则化损失
            u_emb_initial = model.user_emb(users_b)
            i_emb_initial = model.item_emb(pos_b)
            j_emb_initial = model.item_emb(neg_b)

            # 4. 计算总损失
            loss = bpr_loss(
                u_emb_final, i_emb_final, j_emb_final,
                u_emb_initial, i_emb_initial, j_emb_initial,
                reg=reg_lambda
            )

            # 5. 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += float(loss.item())

        scheduler.step()
        avg_loss = total_loss / steps

        if epoch % 20 == 0 or epoch <= 10:
            print(f"  Epoch {epoch:03d} | loss={avg_loss:.4f} | lr={scheduler.get_last_lr()[0]:.6f}")

    # 保存训练好的模型
    models[config['name']] = model
    print(f"模型 {config['name']} 训练完成。")

print("所有模型训练完成。")

# ==============================================================================
# 第6部分：模型融合推荐生成
# ==============================================================================
#
# 说明：
# 教授建议：使用模型融合策略提升推荐效果
# 为每位用户生成 Top-1 推荐；若用户在训练集中无记录（冷启动），回退到全局最热图书。

def generate_model_predictions(model, model_name):
    """为单个模型生成预测分数"""
    print(f"正在生成 {model_name} 的预测...")
    model.eval()
    with torch.no_grad():
        user_all, item_all = model(S_sp, user_feat_tensor, item_feat_tensor)

    # 计算所有用户对所有物品的分数矩阵
    scores_matrix = torch.matmul(user_all, item_all.t())  # [n_users, n_items]
    return scores_matrix

print("\n正在为所有用户生成融合推荐...")
all_user_ids = users['借阅人'].astype(str).unique().tolist()

# 为过滤已交互集合做准备
seen_items_by_user = (
    inter_df.groupby('user_id')['book_id']
    .apply(lambda s: set(s.astype(str).tolist()))
    .to_dict()
)

# 生成各模型的预测分数
model_scores = {}
fusion_weights = {'enhanced_v1': 0.4, 'enhanced_v2': 0.35, 'enhanced_v3': 0.25}

for model_name, model in models.items():
    model_scores[model_name] = generate_model_predictions(model, model_name)

# 教授建议：预测时融合多个模型
print("正在进行模型融合...")
fused_scores = torch.zeros_like(model_scores['enhanced_v1'])
for model_name, scores in model_scores.items():
    weight = fusion_weights[model_name]
    fused_scores += weight * scores

print(f"融合权重：{fusion_weights}")

# 生成推荐
rec_pairs: List[Tuple[str, str]] = []

for uid in all_user_ids:
    if uid in uid2idx:
        uidx = uid2idx[uid]
        user_scores = fused_scores[uidx].clone()  # [n_items]

        # 将已看过物品的分数置为极低值
        for seen_bid in seen_items_by_user.get(uid, set()):
            if seen_bid in bid2idx:
                user_scores[bid2idx[seen_bid]] = -1e9

        # 获取最高分物品
        top_idx = int(torch.argmax(user_scores).item())
        rec_pairs.append((uid, idx2bid[top_idx]))
    else:
        # 冷启动：按全局最热未看过的第一个
        seen = seen_items_by_user.get(uid, set())
        fallback_bid = None
        for it in popular_items:
            if it not in seen:
                fallback_bid = it
                break
        if fallback_bid is None and len(popular_items) > 0:
            fallback_bid = popular_items[0]
        if fallback_bid is not None:
            rec_pairs.append((uid, fallback_bid))

# 构建 DataFrame
recommendations_df = pd.DataFrame(rec_pairs, columns=['user_id', 'book_id'])

# 保存为提交文件
output_filename = 'submission_enhanced.csv'
recommendations_df.to_csv(output_filename, index=False)

print(f"\n已成功生成并保存 {len(recommendations_df)} 条融合推荐结果。")
print(f"输出文件: '{output_filename}'")
print("最终推荐示例：")
print(recommendations_df.head())

# ==============================================================================
# 第7部分：性能分析与对比
# ==============================================================================
print("\n=== 模型改进总结 ===")
print("✅ 已实现教授的所有关键建议：")
print("1. 智能评分构建：基于续借次数和借阅时长的评分系统")
print("2. 混合负采样：70%随机 + 30%热门未交互物品")
print("3. 特征增强：融合用户院系/年级/类型和图书分类信息")
print("4. 超参数优化：embed_dim=128, n_layers=4, 更大batch_size")
print("5. 模型融合：3个变体模型的加权融合")
print("6. 学习率调度：StepLR with gamma=0.8")

print(f"\n模型规模对比：")
print(f"原始模型参数量：{(64 * (n_users + n_items)):,}")
for config in model_configs:
    embed_dim = config['embed_dim']
    # 计算增强模型的参数量（包括特征嵌入）
    main_params = embed_dim * (n_users + n_items)
    feat_params = (16 * len(dept_encoder.classes_) + 8 * len(grade_encoder.classes_) +
                   8 * len(type_encoder.classes_) + 16 * len(cat1_encoder.classes_) +
                   16 * len(cat2_encoder.classes_))
    proj_params = (embed_dim + 32) * embed_dim * 2  # 两个投影层
    total_params = main_params + feat_params + proj_params
    print(f"{config['name']}参数量：{total_params:,}")

print(f"\n评分改进效果：")
print(f"原始评分：全部为1.0")
print(f"智能评分：范围{data_vals.min():.2f}-{data_vals.max():.2f}，平均{data_vals.mean():.2f}")
print(f"评分方差：{data_vals.var():.3f}（方差越大，区分度越好）")
