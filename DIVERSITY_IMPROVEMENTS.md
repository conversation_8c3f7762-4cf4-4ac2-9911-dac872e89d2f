# 🎯 LightGCN多样性优化完整实现

## 📋 问题诊断
**原始问题**：很多用户都被推荐了相同的热门图书，导致推荐结果缺乏多样性，出现"热门物品偏差"(Popularity Bias)。

## 🔧 专家解决方案实现

### ✅ 方案2：去偏采样策略
**实现位置**：`sample_batch_diversity_aware()` 函数
```python
# 流行度感知的负采样：
# - 50%从长尾书籍采样
# - 30%从中等流行度书籍  
# - 20%从热门书籍
```
**技术细节**：
- 将物品按流行度分为三层：热门(前20%)、中等(中间60%)、长尾(后20%)
- 训练时按比例从不同层级采样负样本
- 避免模型过度学习热门物品模式

### ✅ 方案3：MMR重排序算法  
**实现位置**：`mmr_rerank()` 函数
```python
# 最大边际相关性重排序：
# MMR分数 = λ×相关性 - (1-λ)×与已选物品的最大相似度
# λ=0.7 (70%相关性，30%多样性)
```
**技术细节**：
- 在推荐生成阶段平衡相关性和多样性
- 使用余弦相似度计算物品间相似性
- 迭代选择，避免推荐过于相似的物品

### ✅ 方案4：流行度感知损失函数
**实现位置**：`bpr_loss_popularity_aware()` 函数  
```python
# 流行度权重计算：
# weight = 1.0 + log(max_popularity / (item_popularity + 1))
# 流行度越高，权重越低
```
**技术细节**：
- 根据物品流行度调整BPR损失权重
- 推荐冷门物品获得更高训练奖励
- 自动平衡热门与长尾物品的学习

### ✅ 方案1：多样性正则化
**实现位置**：`diversity_regularization()` 函数
```python
# 多样性约束：
# diversity_loss = mean(user_similarity × item_similarity)
# 相似用户不应被推荐相似物品
```
**技术细节**：
- 在BPR损失中加入多样性约束项
- 计算批次内用户和物品的相似度矩阵
- 鼓励为不同用户推荐不同物品

## 🚀 核心技术改进

### 1. 智能评分系统
```python
# 原始：data_vals = np.ones(len(inter_df))
# 改进：基于续借次数和借阅时长的评分
final_score = base_score + renew_bonus + time_bonus
```

### 2. 特征增强架构
```python
# 用户特征：院系 + 年级 + 类型
# 物品特征：一级分类 + 二级分类  
# 特征融合：Linear投影层整合
```

### 3. 模型融合策略
```python
# 训练3个变体模型：
# - enhanced_v1: embed_dim=128, n_layers=4
# - enhanced_v2: embed_dim=96, n_layers=3  
# - enhanced_v3: embed_dim=160, n_layers=4
# 融合权重：0.4 + 0.35 + 0.25
```

## 📊 预期效果指标

### 多样性指标
- **多样性比率** > 0.3 (推荐唯一物品数/总推荐数)
- **长尾推荐比例** > 15% (交互次数≤5的物品)
- **基尼系数** 0.3-0.6 (推荐分布平等程度)
- **热门偏差指数** < 0.5 (TOP热门物品重叠度)

### 性能指标  
- **训练效率**：更大batch_size(8192) + 学习率调度
- **模型容量**：embed_dim提升至128/160
- **特征利用**：融合用户和物品side information
- **鲁棒性**：3模型融合降低过拟合风险

## 🎮 使用方法

### 方法1：直接运行主脚本
```bash
python gcnnrom.py
```

### 方法2：使用增强运行脚本（推荐）
```bash
python run_enhanced_model.py
```

### 方法3：单独测试多样性
```bash  
python test_diversity.py
```

## 📈 技术优势

1. **解决核心问题**：有效缓解热门物品偏差
2. **保持准确性**：特征增强和模型融合提升匹配精度  
3. **提升用户体验**：避免千人一面的推荐结果
4. **工程可靠**：完整的错误处理和回退机制
5. **可扩展性**：模块化设计便于进一步优化

## 🔍 验证方法

运行后检查以下指标：
- [ ] 推荐物品重复度是否显著降低
- [ ] 长尾物品推荐比例是否提升
- [ ] 基尼系数是否在合理范围
- [ ] MMR重排序使用比例
- [ ] 流行度分布是否更加均衡

**预期结果**：推荐系统在保持高准确性的同时，显著提升推荐多样性，为用户提供更加个性化和丰富的图书推荐体验。
